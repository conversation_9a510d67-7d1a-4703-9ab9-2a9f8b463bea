<template>
  <div class="login-div">
    <div v-motion :initial="initial()" :enter="enter(200)">
      <el-input
        type="text"
        class="loign-input mb-4"
        v-model="user"
        :placeholder="transformI18n(i18nType + 'loginAccount', useI18n)"
      >
        <template #prepend
          ><el-icon><UserIcon /></el-icon
        ></template>
      </el-input>
    </div>
    <div class="focus" v-motion :initial="initial()" :enter="enter()">
      <el-input
        type="password"
        class="loign-input mb-4"
        show-password
        v-model="pwd"
        :placeholder="transformI18n(i18nType + 'password', useI18n)"
      >
        <template #prepend
          ><el-icon><Lock /></el-icon
        ></template>
      </el-input>
    </div>
    <div
      class="mb-4"
      v-if="hasBackEndCode()"
      v-motion
      :initial="initial()"
      :enter="enter(200)"
    >
      <div class="flex" style="width: 288px">
        <el-input
          class="loign-input"
          v-model="verifyCode"
          :placeholder="transformI18n(i18nType + 'verifyCode', useI18n)"
        >
          <template #prepend
            ><el-icon><EditPen /></el-icon
          ></template>
        </el-input>
        <div
          @click="refreshCode"
          class="verify-code-time flex items-center absolute h-full"
          style="right: -10px"
        >
          <el-image
            :style="{
              width: verifyCodeImageWidth + 'px',
              height: '100%',
            }"
            :src="`${VITE_APP_BASE_URL_PREFIX}/sys/image/code?randomStr=${randomStr}`"
          >
            <template #placeholder>
              <div class="w-full h-full flex items-center justify-center">
                <sys-icon class="is-loading" type="Loading" />
              </div>
            </template>
          </el-image>
        </div>
      </div>
    </div>
    <div
      v-if="
        slideVerifyShow &&
        (projectSettings.verifyCode === 'front' ||
          projectSettings.verifyCode === 'both')
      "
      class="slide-verify-content"
    >
      <slide-verify
        class="slide-verify"
        ref="block"
        :w="274"
        :h="150"
        :r="10"
        :l="82"
        :slider-text="text"
        :accuracy="accuracy"
        @again="onAgain"
        @success="onSuccess"
        @fail="onFail"
        @refresh="onRefresh"
        :imgs="imgs"
      />
    </div>
    <div class="bottom-div">
      <el-button link type="info" @click="handleRetrieve" class="retrieve-btn"
        >忘记密码？</el-button
      >
      <el-button
        style="margin-right: 8px"
        link
        type="info"
        @click="handleRegister"
        class="retrieve-btn"
        >账号注册</el-button
      >
    </div>
    <div class="set-btn cursor-pointer" @click="onLogin">
      <el-button
        type="primary"
        class="btn"
        :disabled="
          hasFrontCode() &&
          (projectSettings.verifyCode === 'backend' ||
            projectSettings.verifyCode === 'both')
        "
        :loading="loading"
        v-motion
        :initial="initial(10)"
        :enter="enter(400)"
      >
        {{ transformI18n(i18nType + 'hslogin', useI18n) }}99
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PropType, ref, watch, onMounted } from 'vue';
  import { User as UserIcon, Lock, EditPen } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';
  import { VERIFYCODE } from '/@/enums/cacheEnum.ts';
  import { transformI18n } from '/@/utils/i18n';
  import { useUserStore } from '/@/stores/modules/user';
  import { useInterval } from '@vueuse/core';
  // import { DragVerifyActionType } from '/@/components/sys/Verify';
  import { AesEncryption, cacheCipher } from '/@/utils/cipher';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { projectConfigHook } from '/@/hooks/settings/projectConfig';
  import { useAppStoreWithOut } from '/@/stores/modules/app';
  import { LoginEnum } from '/@/enums/appEnum';
  import { loadEnv, warpperEnv } from '@build/index';
  import { useAnimateData } from '/@/views/shares/components/login/index';
  import SlideVerify, { SlideVerifyInstance } from 'vue3-slide-verify';
  import 'vue3-slide-verify/dist/style.css';
  import { getImgApi } from '/@/api/sys';
  import { projectSettings } from '/@/settings/config/projectConfig';
  import img1 from '/img1.png';
  import img2 from '/img2.png';
  import router from '/@/router';
  interface Body {
    username: string;
    password: string;
    randomStr?: number;
    code?: string;
    type?: number;
  }
  const { hasFrontCode, hasBackEndCode } = projectConfigHook();

  const props = defineProps({
    currentType: {
      type: String as PropType<LoginEnum>,
      default: '',
    },
    loginType: {
      type: String as PropType<string>,
      default: '',
    },
  });
  const { initial, enter } = useAnimateData();

  const i18nType = 'buttons.';
  const useI18n = true;
  const verifyCode = ref('');
  const slideVerifyShow = ref(false);
  const userStore = useUserStore();
  const appStore = useAppStoreWithOut();
  const loading = ref<boolean>(false);

  const { VITE_APP_BASE_URL_PREFIX } = warpperEnv(loadEnv());

  const { counter, pause, resume } = useInterval(200, {
    controls: true,
    immediate: false,
  });
  const refreshTime = 60; // 60s 刷新一次

  // 拖拽验证码
  // const el2 = ref<Nullable<DragVerifyActionType>>(null);
  // const elVerify = ref<Nullable<HTMLInputElement>>(null);
  const verifyCodeImageWidth = ref(80);
  const verifyCodeImageBorderWidth = ref(80);
  const tacInit = ref();
  // const successText = ref('');

  const randomStr = ref(new Date().getTime());
  const imgs = ref<string[]>([]);

  const getImgs = async () => {
    const res = await getImgApi();
    console.log(res);
    imgs.value = res;
  };
  if (projectSettings.oss === true) {
    getImgs();
  } else {
    imgs.value = [img1, img2];
  }
  // 图片验证码
  const text = ref('向右滑动 ->');
  const accuracy = ref(5);
  const msg = ref('');
  const block = ref<SlideVerifyInstance>();
  const onAgain = () => {
    msg.value = '检测到非人为操作！ try again';
    // 刷新
    block.value?.refresh();
  };
  const handleRetrieve = () => {
    // router.replace('/user/retrievepassword');
    // router.replace('https://www.baidu.com/');
    router.replace('/user/retrievepassword');
  };
  const handleRegister = () => {
    router.replace('/user/register');
  };
  const onSuccess = (times: number, captchaId) => {
    console.log(encryption.pwdEncryptByAES(pwd.value));
    const body: Body = {
      username: user.value,
      // password: encryption.pwdEncryptByAES(pwd.value),
      password: pwd.value,
      type: appStore.projectConfig?.loginType,
      captchaId,
    };
    if (hasBackEndCode()) {
      // 如果是后端验证码，需要传递相关数据
      body.randomStr = randomStr.value;
      body.code = verifyCode.value ? encryptedSM4(verifyCode.value) : '';
    }
    loading.value = true;
    userStore
      .login(body)
      .finally(() => {
        loading.value = false;
        if (projectSettings.verifyCode === 'slider') {
          // 关闭验证码
          tacInit.value.destroyWindow();
        } else {
          // 刷新验证码
          refreshCode();
        }
      })
      .catch(() => {
        slideVerifyShow.value = false;
      });
    msg.value = `login success, 耗时${(times / 1000).toFixed(1)}s`;
  };

  const onFail = () => {
    msg.value = '验证不通过';
  };

  const onRefresh = () => {
    msg.value = '点击了刷新小图标';
  };
  function refreshCode() {
    counter.value = 0;
    randomStr.value = new Date().getTime();
  }

  watch(
    () => counter.value,
    (value) => {
      if (value <= refreshTime * 5) {
        verifyCodeImageBorderWidth.value =
          verifyCodeImageWidth.value -
          (verifyCodeImageWidth.value / refreshTime / 5) * value;
      } else {
        if (projectSettings.verifyCode !== 'slider') {
          refreshCode();
        }
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  watch(
    () => props.currentType,
    (value) => {
      if (value !== LoginEnum.USER) {
        pause();
        counter.value = 0;
        return;
      }
      resume();
    },
    {
      immediate: true,
      deep: true,
    },
  );
  // const verifyWidth = ref(200);

  // useResizeObserver(elVerify, (entries) => {
  //   const entry = entries[0];
  //   const { width } = entry.contentRect;
  //   verifyWidth.value = width;
  // });

  // function handleSuccess(data: PassingData) {
  //   const { time } = data;
  //   successText.value = `校验成功 耗时${time}秒`;
  //   canLogin.value = true;
  // }

  let user = ref('');
  let pwd = ref('');

  const encryption = new AesEncryption(cacheCipher);
  // const randomStr = ref(new Date().getTime());
  const onLogin = async (): Promise<void> => {
    if (props.loginType === 'USER') {
      if (hasFrontCode()) {
        return;
      }

      if (!user.value) {
        ElMessage({
          type: 'error',
          showClose: true,
          message: '账号/邮箱/身份证号不能为空',
        });
        return;
      }

      if (!pwd.value) {
        ElMessage({
          type: 'error',
          showClose: true,
          message: '密码不能为空',
        });
        return;
      }
      // if(projectSettings.verifyCode==='backend'||projectSettings.verifyCode==='both') {
      //   console.log(222222)
      //   if(!verifyCode.value && VERIFYCODE ) {
      //     ElMessage({
      //       type: 'error',
      //       showClose: true,
      //       message: '验证码不能为空',
      //     });
      //     return;
      //   }else {
      //     slideVerifyShow.value = true;
      //   }
      // } else if(projectSettings.verifyCode==='slider') {
      //   imageLogin();
      // }else {
      //    slideVerifyShow.value = true;
      // }
      if (projectSettings.verifyCode === 'slider') {
        imageLogin();
      } else if (
        projectSettings.verifyCode === 'backend' ||
        projectSettings.verifyCode === 'none'
      ) {
        onSuccess();
      } else {
        if (!verifyCode.value && VERIFYCODE && projectSettings.verifyCode !== 'front') {
          ElMessage({
            type: 'error',
            showClose: true,
            message: '验证码不能为空',
          });
          return;
        } else {
          slideVerifyShow.value = true;
        }
      }
    }
  };
  function imageLogin() {
    const captchaConfig = {
      // 请求验证码接口 ?type=ROTATE
      requestCaptchaDataUrl: '/sys/image/gen?type=RANDOM',
      // 验证验证码接口
      validCaptchaUrl: '/sys/image/check',
      // 绑定的div
      bindEl: '#slider-valid',
      // 验证成功回调函数
      validSuccess: (res, c, t) => {
        console.log('res, c, t', res, c, t);
        onSuccess(t, res?.data?.id);
      },
      // 验证失败的回调函数(可忽略，如果不自定义 validFail 方法时，会使用默认的)
      validFail: (res, c, tac) => {
        // 验证失败后重新拉取验证码
        tac.reloadCaptcha();
      },
      // 刷新按钮回调事件
      btnRefreshFun: (el, tac) => {
        tac.reloadCaptcha();
      },
      // 关闭按钮回调事件
      btnCloseFun: (el, tac) => {
        tac.destroyWindow();
      },
    };

    window.initTAC('static/tac', captchaConfig).then((tac) => {
      tac.init();
      tacInit.value = tac;
    });
  }
  const keydown = (e) => {
    if (e.keyCode === 13) {
      onLogin();
    }
  };
  onMounted(() => {
    window.addEventListener('keydown', keydown);
  });

  defineExpose({ UserIcon, initial });
</script>

<style scoped lang="scss">
  @import url('/src/styles/login/login.css');
  .login-div {
    // :deep(.el-input__wrapper) {
    //   background: #F3F3F4;
    // }
    min-height: 254px;
    :deep(input:-webkit-autofill) {
      width: 190px;
      background: #f3f3f4;
      -webkit-box-shadow: 0 0 0 1000px #f3f3f4 inset !important;
      //  -webkit-text-fill-color: #F3F3F4!important;
      border-radius: 0;
    }
    // :deep(input) {
    //   box-shadow: #F3F3F4;
    // }
    :deep(.el-input__wrapper) {
      background: #f3f3f4;
    }
  }
  .loign-input {
    height: 40px;
  }

  .verify-code {
    width: 25px;
    margin-left: -3px;
  }

  .verify-code-time:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 5px;
    right: 0;
    height: 2px;
    width: v-bind("verifyCodeImageBorderWidth + 'px'");
    background-color: #006cff;
  }

  .login-div {
    position: relative;
  }

  .slide-verify-content {
    position: absolute;
    top: 0;
    background: #ffffff;
    padding: 24px 12px;
    line-height: 1.5;
    text-align: left;
    background-color: #fff;
    z-index: 2147483647;
    box-shadow: 0 0 2px 2px #eee;
    border: 1px solid #eee;
    width: auto;
    height: auto;
  }
  /* .slide-verify {
    width: calc(100% - 24px);
  } */
  .bottom-div {
    width: 100%;
  }

  .retrieve-btn {
    /* text-align: right; */
    float: right;
    padding: 5px 0 15px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #006cff;
    line-height: 20px;
    font-weight: 400;
  }
  .el-icon {
    font-size: 20px;
  }
  .el-input-group__prepend {
    padding: 0 12px;
  }
</style>
<style>
  #slider-valid {
    position: relative;
    top: -150px;
    left: 150px;
  }

  #slider-valid-logo {
    display: none;
  }
</style>
